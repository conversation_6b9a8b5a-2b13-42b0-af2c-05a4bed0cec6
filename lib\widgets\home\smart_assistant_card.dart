import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';

/// المساعد الذكي - Smart Assistant Card
/// 
/// أهم مكون في الصفحة الرئيسية - مركز الذكاء الاصطناعي
/// يتيح للمستخدم طرح أسئلة والحصول على نصائح ذكية
class SmartAssistantCard extends StatefulWidget {
  const SmartAssistantCard({super.key});

  @override
  State<SmartAssistantCard> createState() => _SmartAssistantCardState();
}

class _SmartAssistantCardState extends State<SmartAssistantCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // تأثير التنفس المستمر
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _isPressed ? 0.95 : _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => setState(() => _isPressed = true),
            onTapUp: (_) => setState(() => _isPressed = false),
            onTapCancel: () => setState(() => _isPressed = false),
            onTap: _handleAssistantTap,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppDimensions.paddingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppColors.primary,
                    AppColors.primary.withValues(alpha: 0.8),
                    AppColors.primaryLight,
                  ],
                ),
                borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3 * _glowAnimation.value),
                    blurRadius: 20 * _glowAnimation.value,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: AppColors.shadow.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      // أيقونة الروبوت المتحركة
                      _buildAnimatedRobotIcon(),
                      
                      const SizedBox(width: AppDimensions.marginLarge),
                      
                      // النص والوصف
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'المساعد الذكي',
                                  style: AppTextStyles.headline4.copyWith(
                                    color: AppColors.textOnPrimary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppColors.success.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    'AI',
                                    style: AppTextStyles.bodySmall.copyWith(
                                      color: AppColors.success,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            
                            const SizedBox(height: AppDimensions.marginSmall),
                            
                            Text(
                              'اسأل أي سؤال حول حالتك الصحية أو كيفية استخدام التطبيق',
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                                height: 1.3,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: AppDimensions.marginLarge),
                  
                  // أمثلة على الأسئلة
                  _buildQuestionExamples(),
                  
                  const SizedBox(height: AppDimensions.marginMedium),
                  
                  // زر البدء
                  _buildStartButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedRobotIcon() {
    return Container(
      width: 80,
      height: 80,
      decoration: BoxDecoration(
        color: AppColors.textOnPrimary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(AppDimensions.radiusCircular),
        border: Border.all(
          color: AppColors.textOnPrimary.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // التأثير المتوهج
          AnimatedBuilder(
            animation: _glowAnimation,
            builder: (context, child) {
              return Container(
                width: 60 * _glowAnimation.value,
                height: 60 * _glowAnimation.value,
                decoration: BoxDecoration(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.1 * _glowAnimation.value),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusCircular),
                ),
              );
            },
          ),
          
          // أيقونة الروبوت
          Icon(
            Icons.smart_toy_outlined,
            size: 40,
            color: AppColors.textOnPrimary,
          ),
        ],
      ),
    );
  }

  Widget _buildQuestionExamples() {
    final examples = [
      'كيف أعتني بطرفي الاصطناعي؟',
      'ما هي أفضل التمارين لحالتي؟',
      'أين أجد أقرب مركز علاج؟',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أمثلة على الأسئلة:',
          style: AppTextStyles.bodySmall.copyWith(
            color: AppColors.textOnPrimary.withValues(alpha: 0.8),
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Wrap(
          spacing: 8,
          runSpacing: 6,
          children: examples.map((example) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: AppColors.textOnPrimary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                example,
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.textOnPrimary.withValues(alpha: 0.9),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStartButton() {
    return Container(
      width: double.infinity,
      height: 48,
      decoration: BoxDecoration(
        color: AppColors.textOnPrimary.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: AppColors.textOnPrimary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            color: AppColors.textOnPrimary,
            size: 20,
          ),
          const SizedBox(width: AppDimensions.marginSmall),
          Text(
            'ابدأ المحادثة',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  void _handleAssistantTap() {
    // TODO: Navigate to AI Assistant chat screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('سيتم فتح المساعد الذكي قريباً...'),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
    );
  }
}
