import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/medical_profile_provider.dart';
import '../../theme/app_styles.dart';

/// Step 5: Location Information
/// Collects user's location information
class Step5LocationInfo extends StatefulWidget {
  const Step5LocationInfo({super.key});

  @override
  State<Step5LocationInfo> createState() => _Step5LocationInfoState();
}

class _Step5LocationInfoState extends State<Step5LocationInfo> {
  final _addressController = TextEditingController();
  
  String? _selectedState;
  String? _selectedCity;
  bool _shareLocation = false;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    final provider = context.read<MedicalProfileProvider>();
    final profile = provider.profile;
    
    _selectedState = profile.state;
    _selectedCity = profile.city;
    _addressController.text = profile.address ?? '';
    _shareLocation = profile.shareLocation;
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildStateField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildCityField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildAddressField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildLocationSharingField(),
            const SizedBox(height: AppDimensions.marginXLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.location_on_outlined,
            color: AppColors.primary,
            size: AppDimensions.iconMedium,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Text(
              'معلومات الموقع تساعدنا في العثور على أقرب المراكز الطبية والمختصين لك.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStateField() {
    final provider = context.read<MedicalProfileProvider>();
    final states = provider.getSaudiStates();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المنطقة *',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            color: AppColors.surface,
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedState,
            decoration: const InputDecoration(
              prefixIcon: Icon(Icons.map_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingMedium,
                vertical: AppDimensions.paddingMedium,
              ),
            ),
            hint: const Text('اختر المنطقة'),
            items: states.map((state) {
              return DropdownMenuItem<String>(
                value: state,
                child: Text(state, style: AppTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedState = value;
                _selectedCity = null; // Reset city when state changes
              });
              _updateLocationInfo();
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCityField() {
    final provider = context.read<MedicalProfileProvider>();
    final cities = _selectedState != null 
        ? provider.getCitiesForState(_selectedState!) 
        : <String>[];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المدينة *',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            color: AppColors.surface,
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedCity,
            decoration: const InputDecoration(
              prefixIcon: Icon(Icons.location_city_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingMedium,
                vertical: AppDimensions.paddingMedium,
              ),
            ),
            hint: Text(_selectedState != null ? 'اختر المدينة' : 'اختر المنطقة أولاً'),
            items: cities.map((city) {
              return DropdownMenuItem<String>(
                value: city,
                child: Text(city, style: AppTextStyles.bodyMedium),
              );
            }).toList(),
            onChanged: _selectedState != null ? (value) {
              setState(() {
                _selectedCity = value;
              });
              _updateLocationInfo();
            } : null,
          ),
        ),
      ],
    );
  }

  Widget _buildAddressField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'العنوان التفصيلي (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        TextFormField(
          controller: _addressController,
          textDirection: TextDirection.rtl,
          maxLines: 2,
          decoration: InputDecoration(
            hintText: 'الحي، الشارع، رقم المبنى...',
            prefixIcon: const Padding(
              padding: EdgeInsets.only(bottom: 20),
              child: Icon(Icons.home_outlined),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            filled: true,
            fillColor: AppColors.surface,
          ),
          onChanged: (_) => _updateLocationInfo(),
        ),
      ],
    );
  }

  Widget _buildLocationSharingField() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Icon(
            Icons.my_location_outlined,
            color: AppColors.primary,
            size: AppDimensions.iconMedium,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مشاركة الموقع الجغرافي',
                  style: AppTextStyles.labelMedium.copyWith(
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'للعثور على أقرب المراكز الطبية',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _shareLocation,
            onChanged: (value) {
              setState(() {
                _shareLocation = value;
              });
              _updateLocationInfo();
            },
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  void _updateLocationInfo() {
    final provider = context.read<MedicalProfileProvider>();
    provider.updateLocationInfo(
      state: _selectedState,
      city: _selectedCity,
      address: _addressController.text.trim(),
      shareLocation: _shareLocation,
    );
  }
}
