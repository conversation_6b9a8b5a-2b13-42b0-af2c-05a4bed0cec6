/// App Configuration File
/// Centralized configuration for the Khotwa application
/// Following the guidelines for clean code and maintainability

class AppConfig {
  // App Information
  static const String appName = 'خطوة - Khotwa';
  static const String appVersion = '1.0.0';
  static const String appDescription =
      'تطبيق ذكي لدعم ذوي الإعاقة الحركية وربطهم بالمراكز المتخصصة';

  // Default Language and Localization
  static const String defaultLanguage = 'ar';
  static const bool isRTL = true;

  // Supabase Configuration
  // Khotwa App Project: ibhyrzexaiqwiiyutblg
  static const String supabaseUrl = 'https://ibhyrzexaiqwiiyutblg.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.f6CyHI35MtonPSzWIGFhZ0Z1U651vULQ0WgVSTJbukk';

  // Feature Toggles
  static const bool enableVideoBackground = true;
  static const bool enableEmailVerification = true;
  static const bool enablePasswordReset = true;
  static const bool enableRememberMe = true;

  // User Types
  static const String userTypePatient = 'patient';
  static const String userTypeSpecialist = 'specialist';
  static const String userTypeAdmin = 'admin';

  // Default user type for registration (patients only via app)
  static const String defaultUserType = userTypePatient;

  // Session Configuration
  static const Duration sessionTimeout = Duration(hours: 24);
  static const String sessionKey = 'khotwa_session';

  // UI Configuration
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 8.0;
  static const double defaultBorderRadius = 12.0;

  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);

  // Form Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  // Error Messages (Arabic)
  static const String errorInvalidEmail = 'البريد الإلكتروني غير صحيح';
  static const String errorWeakPassword = 'كلمة المرور ضعيفة جداً';
  static const String errorPasswordTooShort = 'كلمة المرور قصيرة جداً';
  static const String errorNameTooShort = 'الاسم قصير جداً';
  static const String errorNameTooLong = 'الاسم طويل جداً';
  static const String errorEmailAlreadyExists = 'البريد الإلكتروني مسجل مسبقاً';
  static const String errorInvalidCredentials =
      'البريد الإلكتروني أو كلمة المرور غير صحيحة';
  static const String errorNetworkError = 'خطأ في الاتصال بالإنترنت';
  static const String errorUnknown = 'حدث خطأ غير متوقع';

  // Success Messages (Arabic)
  static const String successAccountCreated = 'تم إنشاء الحساب بنجاح';
  static const String successLoginSuccessful = 'تم تسجيل الدخول بنجاح';
  static const String successPasswordResetSent =
      'تم إرسال رابط إعادة تعيين كلمة المرور';
  static const String successEmailVerificationSent =
      'تم إرسال رابط التحقق من البريد الإلكتروني';

  // Button Labels (Arabic)
  static const String buttonStart = 'ابدأ';
  static const String buttonAboutApp = 'حول التطبيق';
  static const String buttonSignUp = 'إنشاء حساب';
  static const String buttonSignIn = 'تسجيل الدخول';
  static const String buttonForgotPassword = 'نسيت كلمة المرور؟';
  static const String buttonResetPassword = 'إعادة تعيين كلمة المرور';
  static const String buttonSend = 'إرسال';
  static const String buttonCancel = 'إلغاء';
  static const String buttonContinue = 'متابعة';
  static const String buttonBack = 'رجوع';

  // Form Labels (Arabic)
  static const String labelFullName = 'الاسم الكامل';
  static const String labelEmail = 'البريد الإلكتروني';
  static const String labelPassword = 'كلمة المرور';
  static const String labelConfirmPassword = 'تأكيد كلمة المرور';

  // Placeholders (Arabic)
  static const String placeholderFullName = 'أدخل اسمك الكامل';
  static const String placeholderEmail = 'أدخل بريدك الإلكتروني';
  static const String placeholderPassword = 'أدخل كلمة المرور';
  static const String placeholderConfirmPassword = 'أعد إدخال كلمة المرور';

  // Screen Titles (Arabic)
  static const String titleWelcome = 'مرحباً بك في خطوة';
  static const String titleSignUp = 'إنشاء حساب جديد';
  static const String titleSignIn = 'تسجيل الدخول';
  static const String titleForgotPassword = 'استعادة كلمة المرور';
  static const String titleEmailVerification = 'التحقق من البريد الإلكتروني';

  // Development Configuration
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableAnalytics = false; // Disable for development

  // API Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration connectionTimeout = Duration(seconds: 10);

  // File Paths
  static const String welcomeVideoPath = 'assets/videos/welcome_video.mp4';
  static const String placeholderImagePath = 'assets/images/placeholder.png';
  static const String logoImagePath = 'assets/images/logo.png';

  // Environment Check
  static bool get isProduction => !isDebugMode;

  // Validation Methods
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static bool isValidPassword(String password) {
    return password.length >= minPasswordLength &&
        password.length <= maxPasswordLength;
  }

  static bool isValidName(String name) {
    return name.length >= minNameLength && name.length <= maxNameLength;
  }
}
