import 'package:flutter/material.dart';
import 'dart:async';
import '../../theme/app_styles.dart';

/// السلايدر التحفيزي - Motivational Slider
/// 
/// يعرض اقتباسات تحفيزية وصور ملهمة للمرضى
/// يعمل تلقائياً ويدوياً مع تأثيرات بصرية جميلة
class MotivationalSlider extends StatefulWidget {
  const MotivationalSlider({super.key});

  @override
  State<MotivationalSlider> createState() => _MotivationalSliderState();
}

class _MotivationalSliderState extends State<MotivationalSlider>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late Timer _autoSlideTimer;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  int _currentIndex = 0;

  // الاقتباسات التحفيزية
  final List<MotivationalQuote> _quotes = [
    MotivationalQuote(
      text: 'القوة لا تأتي من القدرة الجسدية، بل من الإرادة التي لا تقهر',
      author: 'مهاتما غاندي',
      gradient: [Color(0xFF667eea), Color(0xFF764ba2)],
      icon: Icons.psychology_outlined,
    ),
    MotivationalQuote(
      text: 'النجاح ليس نهائياً، والفشل ليس قاتلاً، الشجاعة للاستمرار هي ما يهم',
      author: 'ونستون تشرشل',
      gradient: [Color(0xFF f093fb), Color(0xFF f5576c)],
      icon: Icons.trending_up_outlined,
    ),
    MotivationalQuote(
      text: 'كل إنجاز عظيم كان يُعتبر مستحيلاً حتى تم تحقيقه',
      author: 'نيلسون مانديلا',
      gradient: [Color(0xFF4facfe), Color(0xFF00f2fe)],
      icon: Icons.star_outline,
    ),
    MotivationalQuote(
      text: 'التحدي الأكبر في الحياة هو أن تكون نفسك في عالم يحاول باستمرار جعلك شيئاً آخر',
      author: 'رالف والدو إيمرسون',
      gradient: [Color(0xFF43e97b), Color(0xFF38f9d7)],
      icon: Icons.self_improvement_outlined,
    ),
    MotivationalQuote(
      text: 'الطريقة الوحيدة للقيام بعمل عظيم هي أن تحب ما تفعله',
      author: 'ستيف جوبز',
      gradient: [Color(0xFFfa709a), Color(0xFFfee140)],
      icon: Icons.favorite_outline,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeSlider();
  }

  void _initializeSlider() {
    _pageController = PageController();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _startAutoSlide();
    _animationController.forward();
  }

  void _startAutoSlide() {
    _autoSlideTimer = Timer.periodic(
      const Duration(seconds: 5),
      (timer) {
        if (_currentIndex < _quotes.length - 1) {
          _currentIndex++;
        } else {
          _currentIndex = 0;
        }
        
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            _currentIndex,
            duration: const Duration(milliseconds: 600),
            curve: Curves.easeInOutCubic,
          );
        }
      },
    );
  }

  @override
  void dispose() {
    _autoSlideTimer.cancel();
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        height: 180,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: AppColors.shadow.withValues(alpha: 0.15),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          child: Stack(
            children: [
              // الصفحات
              PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemCount: _quotes.length,
                itemBuilder: (context, index) {
                  return _buildQuoteCard(_quotes[index]);
                },
              ),
              
              // مؤشرات الصفحات
              Positioned(
                bottom: 16,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    _quotes.length,
                    (index) => _buildPageIndicator(index),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuoteCard(MotivationalQuote quote) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: quote.gradient,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // الأيقونة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppDimensions.radiusCircular),
              ),
              child: Icon(
                quote.icon,
                color: Colors.white,
                size: 28,
              ),
            ),
            
            const SizedBox(height: AppDimensions.marginMedium),
            
            // النص التحفيزي
            Text(
              quote.text,
              style: AppTextStyles.bodyLarge.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                height: 1.4,
              ),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: AppDimensions.marginSmall),
            
            // المؤلف
            Text(
              '- ${quote.author}',
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPageIndicator(int index) {
    final isActive = index == _currentIndex;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive 
            ? Colors.white 
            : Colors.white.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

/// نموذج الاقتباس التحفيزي
class MotivationalQuote {
  final String text;
  final String author;
  final List<Color> gradient;
  final IconData icon;

  MotivationalQuote({
    required this.text,
    required this.author,
    required this.gradient,
    required this.icon,
  });
}
