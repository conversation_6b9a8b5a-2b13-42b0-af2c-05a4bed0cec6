import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/app_config.dart';
import '../theme/app_styles.dart';

/// Debug Screen to test Supabase connection
/// This screen helps identify where the "invalid api key" error is coming from
class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  String _status = 'جاري الاختبار...';
  String _details = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _testSupabaseConnection();
  }

  Future<void> _testSupabaseConnection() async {
    setState(() {
      _status = 'جاري اختبار الاتصال...';
      _details = '';
      _isLoading = true;
    });

    try {
      // Step 1: Check configuration
      setState(() {
        _details += '1. فحص الإعدادات:\n';
        _details += 'URL: ${AppConfig.supabaseUrl}\n';
        _details += 'Key: ${AppConfig.supabaseAnonKey.substring(0, 20)}...\n\n';
      });

      // Step 2: Initialize Supabase
      setState(() {
        _details += '2. تهيئة Supabase...\n';
      });

      await Supabase.initialize(
        url: AppConfig.supabaseUrl,
        anonKey: AppConfig.supabaseAnonKey,
      );

      setState(() {
        _details += '✅ تم تهيئة Supabase بنجاح\n\n';
      });

      // Step 3: Test basic connection
      setState(() {
        _details += '3. اختبار الاتصال الأساسي...\n';
      });

      final client = Supabase.instance.client;
      final session = client.auth.currentSession;

      setState(() {
        _details += 'الجلسة الحالية: ${session != null ? 'موجودة' : 'غير موجودة'}\n\n';
      });

      // Step 4: Test database access
      setState(() {
        _details += '4. اختبار الوصول لقاعدة البيانات...\n';
      });

      final response = await client
          .from('medical_centers')
          .select('count')
          .count(CountOption.exact);

      setState(() {
        _details += '✅ تم الوصول لقاعدة البيانات بنجاح\n';
        _details += 'عدد المراكز الطبية: ${response.count}\n\n';
      });

      // Step 5: Test authentication
      setState(() {
        _details += '5. اختبار نظام المصادقة...\n';
      });

      // Try to get user (should be null for unauthenticated user)
      final user = client.auth.currentUser;
      setState(() {
        _details += 'المستخدم الحالي: ${user?.email ?? 'غير مسجل دخول'}\n\n';
      });

      setState(() {
        _status = '✅ جميع الاختبارات نجحت!';
        _details += '🎉 Supabase يعمل بشكل صحيح\n';
        _details += 'يمكنك الآن استخدام التطبيق بشكل طبيعي';
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _status = '❌ فشل في الاتصال';
        _details += '\nخطأ: $e\n\n';
        
        if (e.toString().contains('Invalid API key')) {
          _details += '🔍 تشخيص المشكلة:\n';
          _details += '- المفتاح غير صحيح أو منتهي الصلاحية\n';
          _details += '- تأكد من نسخ المفتاح الصحيح من Supabase Dashboard\n';
          _details += '- تأكد من اختيار "anon public" وليس "service_role"\n';
        } else if (e.toString().contains('Network')) {
          _details += '🔍 تشخيص المشكلة:\n';
          _details += '- مشكلة في الاتصال بالإنترنت\n';
          _details += '- تأكد من اتصالك بالإنترنت\n';
          _details += '- قد يكون هناك حجب للموقع\n';
        } else {
          _details += '🔍 خطأ غير متوقع:\n';
          _details += '- راجع إعدادات Supabase\n';
          _details += '- تأكد من أن المشروع نشط\n';
        }
        
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.textOnPrimary,
          title: const Text('اختبار Supabase'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Status card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                decoration: BoxDecoration(
                  color: _status.contains('✅') 
                      ? AppColors.success.withOpacity(0.1)
                      : _status.contains('❌')
                          ? AppColors.error.withOpacity(0.1)
                          : AppColors.info.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                  border: Border.all(
                    color: _status.contains('✅') 
                        ? AppColors.success
                        : _status.contains('❌')
                            ? AppColors.error
                            : AppColors.info,
                  ),
                ),
                child: Row(
                  children: [
                    if (_isLoading)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    else
                      Icon(
                        _status.contains('✅') 
                            ? Icons.check_circle
                            : _status.contains('❌')
                                ? Icons.error
                                : Icons.info,
                        color: _status.contains('✅') 
                            ? AppColors.success
                            : _status.contains('❌')
                                ? AppColors.error
                                : AppColors.info,
                      ),
                    const SizedBox(width: AppDimensions.marginMedium),
                    Expanded(
                      child: Text(
                        _status,
                        style: AppTextStyles.headline4.copyWith(
                          color: _status.contains('✅') 
                              ? AppColors.success
                              : _status.contains('❌')
                                  ? AppColors.error
                                  : AppColors.info,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: AppDimensions.marginLarge),
              
              // Details
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppDimensions.paddingLarge),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                    border: Border.all(color: AppColors.border),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      _details,
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontFamily: 'monospace',
                        height: 1.5,
                      ),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(height: AppDimensions.marginLarge),
              
              // Retry button
              SizedBox(
                width: double.infinity,
                height: AppDimensions.buttonHeight,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _testSupabaseConnection,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.textOnPrimary,
                  ),
                  child: Text(
                    _isLoading ? 'جاري الاختبار...' : 'إعادة الاختبار',
                    style: AppTextStyles.buttonMedium,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
