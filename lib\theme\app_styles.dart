import 'package:flutter/material.dart';

/// Unified Styling System for Khotwa App
/// Following the guidelines for consistent UI design
/// Supports RTL layout and Arabic typography

/// App Colors - Centralized color definitions
class AppColors {
  // Primary Colors - Professional and accessible
  static const Color primary = Color(0xFF2E7D32); // Green - representing growth and healing
  static const Color primaryLight = Color(0xFF4CAF50);
  static const Color primaryDark = Color(0xFF1B5E20);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF1976D2); // Blue - trust and reliability
  static const Color secondaryLight = Color(0xFF42A5F5);
  static const Color secondaryDark = Color(0xFF0D47A1);
  
  // Accent Colors
  static const Color accent = Color(0xFFFF9800); // Orange - warmth and encouragement
  static const Color accentLight = Color(0xFFFFB74D);
  static const Color accentDark = Color(0xFFE65100);
  
  // Neutral Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  static const Color textOnSecondary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Border and Divider Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFBDBDBD);
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);
  
  // Disabled Colors
  static const Color disabled = Color(0xFFBDBDBD);
  static const Color disabledText = Color(0xFF9E9E9E);
}

/// App Fonts - Font families and weights
class AppFonts {
  // Primary font family for Arabic text
  static const String primaryArabic = 'Tajawal';
  
  // Secondary font family for Arabic text
  static const String secondaryArabic = 'Cairo';
  
  // Default system font as fallback
  static const String systemDefault = 'Roboto';
  
  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
}

/// App Text Styles - Consistent typography
class AppTextStyles {
  // Headline styles
  static const TextStyle headline1 = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 32,
    fontWeight: AppFonts.bold,
    color: AppColors.textPrimary,
    height: 1.2,
  );
  
  static const TextStyle headline2 = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 28,
    fontWeight: AppFonts.bold,
    color: AppColors.textPrimary,
    height: 1.3,
  );
  
  static const TextStyle headline3 = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 24,
    fontWeight: AppFonts.semiBold,
    color: AppColors.textPrimary,
    height: 1.3,
  );
  
  static const TextStyle headline4 = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 20,
    fontWeight: AppFonts.semiBold,
    color: AppColors.textPrimary,
    height: 1.4,
  );
  
  // Body text styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 16,
    fontWeight: AppFonts.regular,
    color: AppColors.textPrimary,
    height: 1.5,
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 14,
    fontWeight: AppFonts.regular,
    color: AppColors.textPrimary,
    height: 1.5,
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 12,
    fontWeight: AppFonts.regular,
    color: AppColors.textSecondary,
    height: 1.4,
  );
  
  // Button text styles
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 16,
    fontWeight: AppFonts.semiBold,
    color: AppColors.textOnPrimary,
  );
  
  static const TextStyle buttonMedium = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 14,
    fontWeight: AppFonts.medium,
    color: AppColors.textOnPrimary,
  );
  
  // Caption and label styles
  static const TextStyle caption = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 12,
    fontWeight: AppFonts.regular,
    color: AppColors.textSecondary,
    height: 1.3,
  );
  
  static const TextStyle label = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 14,
    fontWeight: AppFonts.medium,
    color: AppColors.textPrimary,
  );
  
  // Form field styles
  static const TextStyle inputText = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 16,
    fontWeight: AppFonts.regular,
    color: AppColors.textPrimary,
  );
  
  static const TextStyle inputLabel = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 14,
    fontWeight: AppFonts.medium,
    color: AppColors.textSecondary,
  );
  
  static const TextStyle inputHint = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 16,
    fontWeight: AppFonts.regular,
    color: AppColors.textHint,
  );
  
  // Error text style
  static const TextStyle error = TextStyle(
    fontFamily: AppFonts.primaryArabic,
    fontSize: 12,
    fontWeight: AppFonts.regular,
    color: AppColors.error,
  );
}

/// App Dimensions - Consistent spacing and sizing
class AppDimensions {
  // Padding values
  static const double paddingXSmall = 4.0;
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // Margin values
  static const double marginXSmall = 4.0;
  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;
  static const double marginXLarge = 32.0;
  
  // Border radius values
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  static const double radiusCircular = 50.0;
  
  // Button dimensions
  static const double buttonHeight = 48.0;
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightLarge = 56.0;
  
  // Icon sizes
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  
  // Elevation values
  static const double elevationNone = 0.0;
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  
  // Screen breakpoints for responsive design
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
}
