import 'package:flutter/material.dart';
import '../../theme/app_styles.dart';

/// شبكة الخدمات السريعة - Quick Services Grid
///
/// تعرض الخدمات الأساسية في تصميم مدمج وجذاب:
/// - المراكز الطبية
/// - الاستشارات
/// - الملف الطبي
/// - المحتوى التعليمي
/// - المتجر الطبي
/// - خطة العلاج
class QuickServicesGrid extends StatefulWidget {
  const QuickServicesGrid({super.key});

  @override
  State<QuickServicesGrid> createState() => _QuickServicesGridState();
}

class _QuickServicesGridState extends State<QuickServicesGrid>
    with TickerProviderStateMixin {
  late List<AnimationController> _animationControllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;

  final List<QuickService> _services = [
    QuickService(
      title: 'المراكز الطبية',
      subtitle: 'ابحث عن أقرب مركز',
      icon: Icons.local_hospital_outlined,
      gradient: [Color(0xFF667eea), Color(0xFF764ba2)],
      delay: 0,
    ),
    QuickService(
      title: 'طلب استشارة',
      subtitle: 'احجز مع مختص',
      icon: Icons.medical_services_outlined,
      gradient: [Color(0xFFf093fb), Color(0xFFf5576c)],
      delay: 100,
    ),
    QuickService(
      title: 'الملف الطبي',
      subtitle: 'راجع ملفك الصحي',
      icon: Icons.folder_shared_outlined,
      gradient: [Color(0xFF4facfe), Color(0xFF00f2fe)],
      delay: 200,
    ),
    QuickService(
      title: 'المحتوى التعليمي',
      subtitle: 'فيديوهات ومقالات',
      icon: Icons.school_outlined,
      gradient: [Color(0xFF43e97b), Color(0xFF38f9d7)],
      delay: 300,
    ),
    QuickService(
      title: 'المتجر الطبي',
      subtitle: 'أطراف ومستلزمات',
      icon: Icons.shopping_bag_outlined,
      gradient: [Color(0xFFfa709a), Color(0xFFfee140)],
      delay: 400,
    ),
    QuickService(
      title: 'خطة العلاج',
      subtitle: 'تابع تقدمك',
      icon: Icons.timeline_outlined,
      gradient: [Color(0xFFa8edea), Color(0xFFfed6e3)],
      delay: 500,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationControllers = List.generate(
      _services.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      ),
    );

    _scaleAnimations = _animationControllers.map((controller) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(parent: controller, curve: Curves.elasticOut));
    }).toList();

    _slideAnimations = _animationControllers.map((controller) {
      return Tween<Offset>(
        begin: const Offset(0, 0.5),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();

    // تشغيل الأنيميشن مع تأخير متدرج
    for (int i = 0; i < _animationControllers.length; i++) {
      Future.delayed(Duration(milliseconds: _services[i].delay), () {
        if (mounted) {
          _animationControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 24,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: AppDimensions.marginMedium),
              Text(
                'الخدمات السريعة',
                style: AppTextStyles.headline4.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: AppDimensions.marginLarge),

        // الشبكة
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: AppDimensions.marginMedium,
            mainAxisSpacing: AppDimensions.marginMedium,
            childAspectRatio: 1.1,
          ),
          itemCount: _services.length,
          itemBuilder: (context, index) {
            return AnimatedBuilder(
              animation: _animationControllers[index],
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimations[index].value,
                  child: SlideTransition(
                    position: _slideAnimations[index],
                    child: _buildServiceCard(_services[index], index),
                  ),
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildServiceCard(QuickService service, int index) {
    return GestureDetector(
      onTap: () => _handleServiceTap(service),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: service.gradient,
          ),
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: service.gradient.first.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
            onTap: () => _handleServiceTap(service),
            child: Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingLarge),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الأيقونة
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusMedium,
                      ),
                    ),
                    child: Icon(service.icon, color: Colors.white, size: 24),
                  ),

                  const Spacer(),

                  // النص
                  Text(
                    service.title,
                    style: AppTextStyles.bodyLarge.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 4),

                  Text(
                    service.subtitle,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: AppDimensions.marginSmall),

                  // سهم الانتقال
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusCircular,
                        ),
                      ),
                      child: const Icon(
                        Icons.arrow_back_ios_new,
                        color: Colors.white,
                        size: 16,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleServiceTap(QuickService service) {
    // TODO: Navigate to respective service screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم فتح ${service.title} قريباً...'),
        backgroundColor: service.gradient.first,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
      ),
    );
  }
}

/// نموذج الخدمة السريعة
class QuickService {
  final String title;
  final String subtitle;
  final IconData icon;
  final List<Color> gradient;
  final int delay;

  QuickService({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.gradient,
    required this.delay,
  });
}
