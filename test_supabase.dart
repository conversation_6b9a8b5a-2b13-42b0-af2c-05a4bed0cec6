import 'package:http/http.dart' as http;
import 'dart:convert';

/// أداة اختبار سريعة للتحقق من صحة مفاتيح Supabase
/// شغل هذا الملف للتأكد من أن المفاتيح تعمل بشكل صحيح

void main() async {
  // ضع هنا المفاتيح الجديدة من Supabase Dashboard
  const String supabaseUrl = 'https://ibhyrzexaiqwiiyutblg.supabase.co';
  const String supabaseAnonKey = 'YOUR_ANON_KEY_HERE';
  
  print('🧪 اختبار اتصال Supabase...');
  print('URL: $supabaseUrl');
  print('Key: ${supabaseAnonKey.substring(0, 20)}...');
  
  try {
    final response = await http.get(
      Uri.parse('$supabaseUrl/rest/v1/'),
      headers: {
        'apikey': supabaseAnonKey,
        'Authorization': 'Bearer $supabaseAnonKey',
      },
    );
    
    print('Status Code: ${response.statusCode}');
    print('Response: ${response.body}');
    
    if (response.statusCode == 200) {
      print('✅ نجح الاتصال! المفاتيح صحيحة');
    } else if (response.statusCode == 401) {
      print('❌ خطأ في المصادقة - المفتاح غير صحيح');
    } else {
      print('⚠️  استجابة غير متوقعة');
    }
  } catch (e) {
    print('❌ خطأ في الاتصال: $e');
  }
}
