-- Khotwa App Database Setup
-- Run this script in your Supabase SQL Editor

-- Create profiles table for additional user information
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  full_name TEXT NOT NULL,
  user_type TEXT NOT NULL DEFAULT 'patient' CHECK (user_type IN ('patient', 'specialist', 'admin')),
  phone_number TEXT,
  date_of_birth DATE,
  gender TEXT CHECK (gender IN ('male', 'female')),
  city TEXT,
  address TEXT,
  emergency_contact TEXT,
  emergency_contact_phone TEXT,
  medical_conditions TEXT[],
  medications TEXT[],
  notes TEXT,
  profile_image_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles table
CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Create function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name, user_type)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', 'مستخدم جديد'),
    COALESCE(NEW.raw_user_meta_data->>'user_type', 'patient')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to call the function on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create medical centers table
CREATE TABLE IF NOT EXISTS public.medical_centers (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  address TEXT NOT NULL,
  city TEXT NOT NULL,
  phone_number TEXT,
  email TEXT,
  website TEXT,
  services TEXT[] NOT NULL DEFAULT '{}',
  specialties TEXT[] NOT NULL DEFAULT '{}',
  rating DECIMAL(2,1) DEFAULT 0.0,
  image_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for medical centers
ALTER TABLE public.medical_centers ENABLE ROW LEVEL SECURITY;

-- Allow all authenticated users to read medical centers
CREATE POLICY "Authenticated users can view medical centers" ON public.medical_centers
  FOR SELECT TO authenticated USING (is_active = true);

-- Create specialists table
CREATE TABLE IF NOT EXISTS public.specialists (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  medical_center_id UUID REFERENCES public.medical_centers(id) ON DELETE SET NULL,
  specialization TEXT NOT NULL,
  license_number TEXT,
  years_of_experience INTEGER DEFAULT 0,
  bio TEXT,
  consultation_fee DECIMAL(10,2),
  is_available BOOLEAN DEFAULT true,
  rating DECIMAL(2,1) DEFAULT 0.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for specialists
ALTER TABLE public.specialists ENABLE ROW LEVEL SECURITY;

-- Allow all authenticated users to read active specialists
CREATE POLICY "Authenticated users can view specialists" ON public.specialists
  FOR SELECT TO authenticated USING (is_available = true);

-- Allow specialists to update their own profile
CREATE POLICY "Specialists can update own profile" ON public.specialists
  FOR UPDATE USING (auth.uid() = user_id);

-- Create appointments table
CREATE TABLE IF NOT EXISTS public.appointments (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  patient_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  specialist_id UUID REFERENCES public.specialists(id) ON DELETE CASCADE NOT NULL,
  medical_center_id UUID REFERENCES public.medical_centers(id) ON DELETE SET NULL,
  appointment_date DATE NOT NULL,
  appointment_time TIME NOT NULL,
  duration_minutes INTEGER DEFAULT 60,
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'cancelled', 'completed', 'no_show')),
  notes TEXT,
  patient_notes TEXT,
  specialist_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for appointments
ALTER TABLE public.appointments ENABLE ROW LEVEL SECURITY;

-- Patients can view their own appointments
CREATE POLICY "Patients can view own appointments" ON public.appointments
  FOR SELECT USING (auth.uid() = patient_id);

-- Patients can create appointments
CREATE POLICY "Patients can create appointments" ON public.appointments
  FOR INSERT WITH CHECK (auth.uid() = patient_id);

-- Patients can update their own appointments (limited fields)
CREATE POLICY "Patients can update own appointments" ON public.appointments
  FOR UPDATE USING (auth.uid() = patient_id);

-- Specialists can view appointments assigned to them
CREATE POLICY "Specialists can view assigned appointments" ON public.appointments
  FOR SELECT USING (
    specialist_id IN (
      SELECT id FROM public.specialists WHERE user_id = auth.uid()
    )
  );

-- Specialists can update appointments assigned to them
CREATE POLICY "Specialists can update assigned appointments" ON public.appointments
  FOR UPDATE USING (
    specialist_id IN (
      SELECT id FROM public.specialists WHERE user_id = auth.uid()
    )
  );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON public.profiles
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_medical_centers_updated_at
  BEFORE UPDATE ON public.medical_centers
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_specialists_updated_at
  BEFORE UPDATE ON public.specialists
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_appointments_updated_at
  BEFORE UPDATE ON public.appointments
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Insert sample medical centers
INSERT INTO public.medical_centers (name, description, address, city, phone_number, services, specialties) VALUES
('مركز الأطراف الاصطناعية المتقدم', 'مركز متخصص في تركيب وصيانة الأطراف الاصطناعية الحديثة', 'شارع الملك فهد، حي الملز', 'الرياض', '+966112345678', 
 ARRAY['الأطراف الاصطناعية', 'التأهيل الحركي', 'العلاج الفيزيائي'], 
 ARRAY['أطراف علوية', 'أطراف سفلية', 'تأهيل حركي']),
 
('مركز التأهيل الطبي الشامل', 'مركز شامل للعلاج الفيزيائي والتأهيل الحركي', 'طريق الأمير محمد بن عبدالعزيز', 'جدة', '+966126789012',
 ARRAY['العلاج الفيزيائي', 'التأهيل الحركي', 'العلاج الوظيفي'],
 ARRAY['إصابات الحبل الشوكي', 'إصابات الدماغ', 'أمراض العضلات']),
 
('مستشفى الملك فيصل التخصصي', 'مستشفى متخصص في جراحة العظام والتأهيل', 'شارع التخصصي', 'الرياض', '+966114647272',
 ARRAY['جراحة العظام', 'التأهيل الطبي', 'الأطراف الاصطناعية'],
 ARRAY['جراحة العمود الفقري', 'جراحة المفاصل', 'طب الأطفال']);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_profiles_user_type ON public.profiles(user_type);
CREATE INDEX IF NOT EXISTS idx_appointments_patient_id ON public.appointments(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointments_specialist_id ON public.appointments(specialist_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON public.appointments(appointment_date);
CREATE INDEX IF NOT EXISTS idx_medical_centers_city ON public.medical_centers(city);
CREATE INDEX IF NOT EXISTS idx_specialists_specialization ON public.specialists(specialization);
