import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import '../theme/app_styles.dart';
import '../theme/app_icons.dart';
import '../config/app_config.dart';
import 'sign_in_screen.dart';
import 'about_app_screen.dart';

/// Welcome Screen - First screen users see
/// Features video background and main navigation buttons
/// Supports RTL layout and Arabic typography

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({super.key});

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen>
    with TickerProviderStateMixin {
  VideoPlayerController? _videoController;
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  bool _isVideoInitialized = false;
  bool _showFallbackBackground = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVideo();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: AppConfig.longAnimationDuration,
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: AppConfig.mediumAnimationDuration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) _slideController.forward();
    });
  }

  void _initializeVideo() {
    if (AppConfig.enableVideoBackground) {
      try {
        _videoController = VideoPlayerController.asset(
          AppConfig.welcomeVideoPath,
        );
        
        _videoController!.initialize().then((_) {
          if (mounted) {
            setState(() {
              _isVideoInitialized = true;
            });
            _videoController!.setLooping(true);
            _videoController!.play();
          }
        }).catchError((error) {
          if (mounted) {
            setState(() {
              _showFallbackBackground = true;
            });
          }
        });
      } catch (e) {
        setState(() {
          _showFallbackBackground = true;
        });
      }
    } else {
      setState(() {
        _showFallbackBackground = true;
      });
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Stack(
          children: [
            // Background
            _buildBackground(),
            
            // Overlay gradient
            _buildOverlay(),
            
            // Content
            _buildContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    if (_isVideoInitialized && _videoController != null) {
      return SizedBox.expand(
        child: FittedBox(
          fit: BoxFit.cover,
          child: SizedBox(
            width: _videoController!.value.size.width,
            height: _videoController!.value.size.height,
            child: VideoPlayer(_videoController!),
          ),
        ),
      );
    } else if (_showFallbackBackground) {
      return Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.primary,
              AppColors.primaryDark,
              AppColors.secondary,
            ],
          ),
        ),
      );
    } else {
      return Container(
        color: AppColors.primary,
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.textOnPrimary),
          ),
        ),
      );
    }
  }

  Widget _buildOverlay() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.3),
            Colors.black.withOpacity(0.6),
            Colors.black.withOpacity(0.8),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingLarge),
        child: Column(
          children: [
            const Spacer(flex: 2),
            
            // App logo and title
            FadeTransition(
              opacity: _fadeAnimation,
              child: _buildAppHeader(),
            ),
            
            const Spacer(flex: 3),
            
            // Action buttons
            SlideTransition(
              position: _slideAnimation,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: _buildActionButtons(),
              ),
            ),
            
            const Spacer(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppHeader() {
    return Column(
      children: [
        // App icon/logo placeholder
        Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: AppColors.textOnPrimary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(AppDimensions.radiusXLarge),
            border: Border.all(
              color: AppColors.textOnPrimary.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: const Icon(
            Icons.accessibility_new,
            size: 60,
            color: AppColors.textOnPrimary,
          ),
        ),
        
        const SizedBox(height: AppDimensions.marginLarge),
        
        // App title
        Text(
          AppConfig.titleWelcome,
          style: AppTextStyles.headline1.copyWith(
            color: AppColors.textOnPrimary,
            fontSize: 36,
            fontWeight: AppFonts.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: AppDimensions.marginMedium),
        
        // App description
        Text(
          AppConfig.appDescription,
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textOnPrimary.withOpacity(0.9),
            height: 1.6,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Start button
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeightLarge,
          child: ElevatedButton(
            onPressed: _navigateToSignIn,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.accent,
              foregroundColor: AppColors.textOnPrimary,
              elevation: AppDimensions.elevationMedium,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  AppConfig.buttonStart,
                  style: AppTextStyles.buttonLarge.copyWith(
                    fontSize: 18,
                    fontWeight: AppFonts.bold,
                  ),
                ),
                const SizedBox(width: AppDimensions.marginSmall),
                const Icon(
                  Icons.arrow_forward,
                  size: AppDimensions.iconMedium,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: AppDimensions.marginMedium),
        
        // About app button
        SizedBox(
          width: double.infinity,
          height: AppDimensions.buttonHeight,
          child: OutlinedButton(
            onPressed: _navigateToAboutApp,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textOnPrimary,
              side: const BorderSide(
                color: AppColors.textOnPrimary,
                width: 2,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  AppConfig.buttonAboutApp,
                  style: AppTextStyles.buttonMedium.copyWith(
                    color: AppColors.textOnPrimary,
                  ),
                ),
                const SizedBox(width: AppDimensions.marginSmall),
                const Icon(
                  Icons.info_outline,
                  size: AppDimensions.iconMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToSignIn() {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const SignInScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          const begin = Offset(1.0, 0.0);
          const end = Offset.zero;
          const curve = Curves.easeInOut;

          var tween = Tween(begin: begin, end: end).chain(
            CurveTween(curve: curve),
          );

          return SlideTransition(
            position: animation.drive(tween),
            child: child,
          );
        },
        transitionDuration: AppConfig.mediumAnimationDuration,
      ),
    );
  }

  void _navigateToAboutApp() {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            const AboutAppScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: AppConfig.shortAnimationDuration,
      ),
    );
  }
}
