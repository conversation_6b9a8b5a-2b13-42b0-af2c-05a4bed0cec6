import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/medical_profile_provider.dart';
import '../../theme/app_styles.dart';
import '../../config/app_config.dart';

/// Step 2: Medical Condition
/// Collects information about disability type, injury date, and medical details
class Step2MedicalCondition extends StatefulWidget {
  const Step2MedicalCondition({super.key});

  @override
  State<Step2MedicalCondition> createState() => _Step2MedicalConditionState();
}

class _Step2MedicalConditionState extends State<Step2MedicalCondition> {
  final _medicalDetailsController = TextEditingController();
  final _currentConditionController = TextEditingController();
  
  String? _selectedDisabilityType;
  DateTime? _selectedInjuryDate;

  @override
  void initState() {
    super.initState();
    _loadExistingData();
  }

  void _loadExistingData() {
    final provider = context.read<MedicalProfileProvider>();
    final profile = provider.profile;
    
    _selectedDisabilityType = profile.disabilityType;
    _selectedInjuryDate = profile.injuryDate;
    _medicalDetailsController.text = profile.medicalDetails ?? '';
    _currentConditionController.text = profile.currentCondition ?? '';
  }

  @override
  void dispose() {
    _medicalDetailsController.dispose();
    _currentConditionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildDisabilityTypeField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildInjuryDateField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildMedicalDetailsField(),
            const SizedBox(height: AppDimensions.marginLarge),
            _buildCurrentConditionField(),
            const SizedBox(height: AppDimensions.marginXLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.paddingLarge),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.medical_information_outlined,
            color: AppColors.primary,
            size: AppDimensions.iconMedium,
          ),
          const SizedBox(width: AppDimensions.marginMedium),
          Expanded(
            child: Text(
              'معلومات حالتك الصحية تساعدنا في تقديم أفضل الاستشارات والخدمات المناسبة لك.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisabilityTypeField() {
    final provider = context.read<MedicalProfileProvider>();
    final disabilityTypes = provider.getDisabilityTypes();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الإعاقة الحركية *',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.border),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            color: AppColors.surface,
          ),
          child: DropdownButtonFormField<String>(
            value: _selectedDisabilityType,
            decoration: const InputDecoration(
              prefixIcon: Icon(Icons.accessibility_outlined),
              border: InputBorder.none,
              contentPadding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingMedium,
                vertical: AppDimensions.paddingMedium,
              ),
            ),
            hint: const Text('اختر نوع الإعاقة'),
            items: disabilityTypes.map((type) {
              return DropdownMenuItem<String>(
                value: type,
                child: Text(
                  type,
                  style: AppTextStyles.bodyMedium,
                ),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedDisabilityType = value;
              });
              _updateMedicalCondition();
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'يرجى اختيار نوع الإعاقة';
              }
              return null;
            },
          ),
        ),
      ],
    );
  }

  Widget _buildInjuryDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تاريخ الإصابة (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        GestureDetector(
          onTap: _selectInjuryDate,
          child: Container(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.border),
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              color: AppColors.surface,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.event_outlined,
                  color: AppColors.textSecondary,
                  size: AppDimensions.iconMedium,
                ),
                const SizedBox(width: AppDimensions.marginMedium),
                Expanded(
                  child: Text(
                    _selectedInjuryDate != null
                        ? '${_selectedInjuryDate!.day}/${_selectedInjuryDate!.month}/${_selectedInjuryDate!.year}'
                        : 'اختر تاريخ الإصابة (اختياري)',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: _selectedInjuryDate != null 
                          ? AppColors.textPrimary 
                          : AppColors.textSecondary,
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: AppColors.textSecondary,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMedicalDetailsField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تفاصيل الحالة الطبية (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        TextFormField(
          controller: _medicalDetailsController,
          textDirection: TextDirection.rtl,
          maxLines: 4,
          decoration: InputDecoration(
            hintText: 'اكتب تفاصيل إضافية عن حالتك الطبية، سبب الإصابة، أو أي معلومات مهمة...',
            prefixIcon: const Padding(
              padding: EdgeInsets.only(bottom: 60),
              child: Icon(Icons.description_outlined),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            filled: true,
            fillColor: AppColors.surface,
            alignLabelWithHint: true,
          ),
          onChanged: (_) => _updateMedicalCondition(),
        ),
      ],
    );
  }

  Widget _buildCurrentConditionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الوضع الحالي والصعوبات (اختياري)',
          style: AppTextStyles.labelLarge.copyWith(
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: AppDimensions.marginSmall),
        TextFormField(
          controller: _currentConditionController,
          textDirection: TextDirection.rtl,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'اكتب عن وضعك الحالي، الصعوبات التي تواجهها، أو التحسينات التي تحتاجها...',
            prefixIcon: const Padding(
              padding: EdgeInsets.only(bottom: 40),
              child: Icon(Icons.psychology_outlined),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            filled: true,
            fillColor: AppColors.surface,
            alignLabelWithHint: true,
          ),
          onChanged: (_) => _updateMedicalCondition(),
        ),
      ],
    );
  }

  Future<void> _selectInjuryDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedInjuryDate ?? DateTime.now().subtract(const Duration(days: 365)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 80)),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppColors.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedInjuryDate) {
      setState(() {
        _selectedInjuryDate = picked;
      });
      _updateMedicalCondition();
    }
  }

  void _updateMedicalCondition() {
    final provider = context.read<MedicalProfileProvider>();
    provider.updateMedicalCondition(
      disabilityType: _selectedDisabilityType,
      injuryDate: _selectedInjuryDate,
      medicalDetails: _medicalDetailsController.text.trim(),
      currentCondition: _currentConditionController.text.trim(),
    );
  }
}
